# Nobify

Nobify is a personal crypto management tool designed to help users track and manage their airdrop results, portfolio investments, and important notifications. It provides a simple, intuitive web-based platform for crypto enthusiasts to organize their holdings, monitor performance, and stay informed about key crypto events.

## Features

- **Nobify Dashboard:** Central hub displaying key statistics and an overview of your crypto activities, including portfolio value, airdrop claims, and recent alerts.
- **Nobify Airdrops:** Track upcoming, ongoing, and completed airdrops with eligibility criteria, deadlines, and claim status.
- **Nobify Portfolio:** Manage and analyze your crypto investments across multiple wallets with real-time data and historical performance charts.
- **Nobify Alerts:** Set custom notifications for token price changes, airdrop deadlines, and other important events.

## Technical Architecture

- **Frontend:** Built with React and Vite, using Tailwind CSS for styling and Recharts for data visualization.
- **Backend:** Fastify framework with integration to third-party APIs like CoinGecko and CoinMarketCap.
- **Database:** SQLite with Prisma ORM for storing user data, airdrops, portfolio, and alerts.
- **Notifications:** Firebase Cloud Messaging for push notifications and SendGrid for email alerts.

## Getting Started

To get started with <PERSON><PERSON><PERSON>, clone the repository and follow the setup instructions in the respective frontend and backend directories.

### Prerequisites

- Node.js (>= 14.17.0)
- npm (>= 6.14.13)
- SQLite (included with Prisma)

### Setup

\\\ash
# Clone the repository
git clone https://github.com/Nobhokleng/Nobify.git

# Navigate to the project directory
cd Nobify

# Install dependencies
npm install

# Start the development servers
npm run dev --prefix backend
npm run dev --prefix frontend
\\\

## Project Structure

\\\
Nobify/
├── frontend/          # React frontend application
├── backend/           # Fastify backend API
├── mobile/            # React Native mobile app
├── tasks/             # Task management files
└── docs/              # Documentation
\\\

## Roadmap

### Phase 1: Research & Branding
- Conduct market research and competitor analysis
- Develop the brand identity and visual design

### Phase 2: Core Features Development
- Develop the Nobify Dashboard, Airdrops, Portfolio, and Alerts features
- Integrate third-party APIs and services

### Phase 3: Testing & Launch
- Conduct unit testing, integration testing, and UI testing
- Launch the application and deploy to production

### Phase 4: Iterate & Improve
- Gather user feedback and iterate on the application
- Continuously improve and refine the application

## License

This project is licensed under the MIT License.

---

For detailed requirements and technical specifications, refer to the [Nobify Product Requirements Document](scripts/nobify_prd.txt).
